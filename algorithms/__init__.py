"""
算法包 - 重构后的分类结构

该包包含所有迁移到统一框架的scaling分析算法，按类别组织：

🔬 Conventional_ML/ - 传统机器学习算法
    - SVM (支持向量机)
    - DFT/FFT (离散傅里叶变换)
    - PCA (主成分分析)

🧠 DNN/ - 深度神经网络算法  
    - LSTM (长短期记忆网络)
    - StyleGAN (风格生成对抗网络)

🤖 LLM/ - 大语言模型
    - Falcon-7B
    - Gemma-7B
    - LLaMA-7B

📊 Dense_Linear_Algebra/ - 稠密线性代数算法
    - GeMM (通用矩阵乘法)

使用方法:
    from algorithms.Conventional_ML import SVMScalingAnalyzer
    from algorithms.DNN import LSTMScalingAnalyzer
    from algorithms.LLM import FalconScalingAnalyzer
    from algorithms.Dense_Linear_Algebra import GemmScalingAnalyzer
    
    # 或者从分类包导入
    from algorithms import Conventional_ML, DNN, LLM, Dense_Linear_Algebra
"""

# 从子包导入所有算法
from .Conventional_ML import (
    SVMScalingAnalyzer,
    DFTScalingAnalyzer, 
    FFTScalingAnalyzer,
    PCAScalingAnalyzer
)

from .DNN import (
    LSTMScalingAnalyzer,
    StyleGANScalingAnalyzer
)

from .LLM import (
    FalconScalingAnalyzer,
    GemmaScalingAnalyzer,
    LlamaScalingAnalyzer
)

from .Dense_Linear_Algebra import (
    GemmScalingAnalyzer,
    LUScalingAnalyzer,
    CholeskyScalingAnalyzer,
    SVDScalingAnalyzer
)

from .Sparse_Linear_Algebra import (
    SpMVScalingAnalyzer
)

from .Spectral_Methods import (
    ConvolutionScalingAnalyzer
)

from .Graph_Tree_Algo import (
    BFSScalingAnalyzer
)

from .Sorting import (
    MergeSortScalingAnalyzer,
    RadixSortScalingAnalyzer
)

from .Database import (
    HashJoinScalingAnalyzer
)

# 提供方便的分类访问
from . import Conventional_ML
from . import DNN
from . import LLM
from . import Dense_Linear_Algebra
from . import Sparse_Linear_Algebra
from . import Spectral_Methods
from . import Sorting

__all__ = [
    # 传统机器学习
    'SVMScalingAnalyzer',
    'DFTScalingAnalyzer',
    'FFTScalingAnalyzer', 
    'PCAScalingAnalyzer',
    
    # 深度神经网络
    'LSTMScalingAnalyzer',
    'StyleGANScalingAnalyzer',
    
    # 大语言模型
    'FalconScalingAnalyzer',
    'GemmaScalingAnalyzer',
    'LlamaScalingAnalyzer',
    
    # 稠密线性代数
    'GemmScalingAnalyzer',
    'LUScalingAnalyzer',
    'CholeskyScalingAnalyzer',
    'SVDScalingAnalyzer',
    
    # 稀疏线性代数
    'SpMVScalingAnalyzer',
    
    # 谱方法
    'ConvolutionScalingAnalyzer',
    
    # 分类包
    'Conventional_ML',
    'DNN',
    'LLM',
    'Dense_Linear_Algebra',
    'Sparse_Linear_Algebra',
    'Spectral_Methods',
    'Sorting',

    # 图与树算法
    'BFSScalingAnalyzer',

    # 排序算法
    'MergeSortScalingAnalyzer',
    'RadixSortScalingAnalyzer',

    # 数据库算法
    'HashJoinScalingAnalyzer'
]

# 算法映射表，用于批处理脚本
ALGORITHM_MAPPING = {
    # 传统机器学习
    'svm': ('Conventional_ML', 'SVMScalingAnalyzer'),
    'dft': ('Conventional_ML', 'DFTScalingAnalyzer'),
    'fft': ('Conventional_ML', 'FFTScalingAnalyzer'),
    'pca': ('Conventional_ML', 'PCAScalingAnalyzer'),
    
    # 深度神经网络  
    'lstm': ('DNN', 'LSTMScalingAnalyzer'),
    'stylegan': ('DNN', 'StyleGANScalingAnalyzer'),
    
    # 大语言模型
    'falcon': ('LLM', 'FalconScalingAnalyzer'),
    'gemma': ('LLM', 'GemmaScalingAnalyzer'),
    'llama': ('LLM', 'LlamaScalingAnalyzer'),
    
    # 稠密线性代数
    'gemm': ('Dense_Linear_Algebra', 'GemmScalingAnalyzer'),
    'lu': ('Dense_Linear_Algebra', 'LUScalingAnalyzer'),
    'cholesky': ('Dense_Linear_Algebra', 'CholeskyScalingAnalyzer'),
    'svd': ('Dense_Linear_Algebra', 'SVDScalingAnalyzer'),
    
    # 稀疏线性代数
    'spmv': ('Sparse_Linear_Algebra', 'SpMVScalingAnalyzer'),
    
    # 谱方法
    'convolution': ('Spectral_Methods', 'ConvolutionScalingAnalyzer'),

    # 排序算法
    'merge_sort': ('Sorting', 'MergeSortScalingAnalyzer'),
    'radix_sort': ('Sorting', 'RadixSortScalingAnalyzer'),

    # 数据库算法
    'hash_join': ('Database', 'HashJoinScalingAnalyzer'),
}

def get_analyzer_class(algorithm_name: str):
    """根据算法名称获取对应的分析器类"""
    if algorithm_name.lower() in ALGORITHM_MAPPING:
        category, class_name = ALGORITHM_MAPPING[algorithm_name.lower()]
        if category == 'Conventional_ML':
            return getattr(Conventional_ML, class_name)
        elif category == 'DNN':
            return getattr(DNN, class_name)
        elif category == 'LLM':
            return getattr(LLM, class_name)
        elif category == 'Dense_Linear_Algebra':
            return getattr(Dense_Linear_Algebra, class_name)
    raise ValueError(f"Unknown algorithm: {algorithm_name}")

def list_algorithms():
    """列出所有可用的算法"""
    return {
        'Conventional_ML': ['svm', 'dft', 'fft', 'pca'],
        'DNN': ['lstm', 'stylegan'], 
        'LLM': ['falcon', 'gemma', 'llama'],
        'Dense_Linear_Algebra': ['gemm', 'cholesky'],
        'Sparse_Linear_Algebra': ['spmv'],
        'Spectral_Methods': ['convolution']
    } 
