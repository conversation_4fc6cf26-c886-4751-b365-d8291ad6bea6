[{"input_size": 100, "algorithm_name": "TPCH-Q6-uniform-sel0.020", "timestamp": 1753655842.5691605, "execution_time_ms": 0.12541720643639565, "setup_time_ms": 0.4946412518620491, "cleanup_time_ms": 23.601830005645752, "total_time_ms": 24.221888463944197, "baseline_memory_mb": 410.71484375, "peak_memory_mb": 411.6328125, "memory_increment_mb": 0.91796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.02, "data_distribution": "uniform", "use_indexes": false, "input_size": 100, "total_rows": 100, "rows_scanned": 100, "rows_filtered": 1, "qualifying_rows": 1, "scan_ratio": 1.0, "filter_efficiency": 0.01, "actual_selectivity": 0.01, "expected_selectivity": 0.02, "selectivity_accuracy": 0.99, "comparisons": 228, "arithmetic_operations": 2, "memory_accesses": 101, "index_lookups": 0, "comparisons_per_row": 2.28, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.99, "index_effectiveness": 0, "scan_time_ms": 0.082, "revenue": 4216.26, "avg_revenue_per_row": 4216.26, "price_variance": 0, "discount_variance": 0, "quantity_variance": 0, "effective_scan_size": 100, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=100]", "theoretical_space_complexity": "O(k) [k≈2]", "theoretical_memory_mb": 0.0001220703125, "efficiency_ratio": 0.00013297872340425532}, {"input_size": 200, "algorithm_name": "TPCH-Q6-uniform-sel0.020", "timestamp": 1753655842.7653997, "execution_time_ms": 0.16724132001399994, "setup_time_ms": 0.9492370299994946, "cleanup_time_ms": 23.083871230483055, "total_time_ms": 24.20034958049655, "baseline_memory_mb": 411.6328125, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.09375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.02, "data_distribution": "uniform", "use_indexes": false, "input_size": 200, "total_rows": 200, "rows_scanned": 200, "rows_filtered": 4, "qualifying_rows": 4, "scan_ratio": 1.0, "filter_efficiency": 0.02, "actual_selectivity": 0.02, "expected_selectivity": 0.02, "selectivity_accuracy": 1.0, "comparisons": 472, "arithmetic_operations": 8, "memory_accesses": 204, "index_lookups": 0, "comparisons_per_row": 2.36, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 0.134, "revenue": 11177.33, "avg_revenue_per_row": 2794.33, "price_variance": 344293032.16, "discount_variance": 0.0, "quantity_variance": 8.69, "effective_scan_size": 200, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=200]", "theoretical_space_complexity": "O(k) [k≈4]", "theoretical_memory_mb": 0.000244140625, "efficiency_ratio": 0.0026041666666666665}, {"input_size": 300, "algorithm_name": "TPCH-Q6-uniform-sel0.020", "timestamp": 1753655842.9297998, "execution_time_ms": 0.2203899435698986, "setup_time_ms": 1.378566026687622, "cleanup_time_ms": 23.088973946869373, "total_time_ms": 24.687929917126894, "baseline_memory_mb": 411.7265625, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.02, "data_distribution": "uniform", "use_indexes": false, "input_size": 300, "total_rows": 300, "rows_scanned": 300, "rows_filtered": 6, "qualifying_rows": 6, "scan_ratio": 1.0, "filter_efficiency": 0.02, "actual_selectivity": 0.02, "expected_selectivity": 0.02, "selectivity_accuracy": 1.0, "comparisons": 720, "arithmetic_operations": 12, "memory_accesses": 306, "index_lookups": 0, "comparisons_per_row": 2.4, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 0.191, "revenue": 12857.22, "avg_revenue_per_row": 2142.87, "price_variance": 579290927.81, "discount_variance": 0.0, "quantity_variance": 28.56, "effective_scan_size": 300, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=300]", "theoretical_space_complexity": "O(k) [k≈6]", "theoretical_memory_mb": 0.0003662109375, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "TPCH-Q6-uniform-sel0.020", "timestamp": 1753655843.093979, "execution_time_ms": 0.27270857244729996, "setup_time_ms": 1.8054717220366001, "cleanup_time_ms": 23.106709122657776, "total_time_ms": 25.184889417141676, "baseline_memory_mb": 411.7265625, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.02, "data_distribution": "uniform", "use_indexes": false, "input_size": 400, "total_rows": 400, "rows_scanned": 400, "rows_filtered": 8, "qualifying_rows": 8, "scan_ratio": 1.0, "filter_efficiency": 0.02, "actual_selectivity": 0.02, "expected_selectivity": 0.02, "selectivity_accuracy": 1.0, "comparisons": 962, "arithmetic_operations": 16, "memory_accesses": 408, "index_lookups": 0, "comparisons_per_row": 2.4, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 0.241, "revenue": 21078.45, "avg_revenue_per_row": 2634.81, "price_variance": 725526417.75, "discount_variance": 0.0, "quantity_variance": 35.23, "effective_scan_size": 400, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=400]", "theoretical_space_complexity": "O(k) [k≈8]", "theoretical_memory_mb": 0.00048828125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "TPCH-Q6-uniform-sel0.020", "timestamp": 1753655843.2587001, "execution_time_ms": 0.31654583290219307, "setup_time_ms": 2.221310045570135, "cleanup_time_ms": 22.94674515724182, "total_time_ms": 25.48460103571415, "baseline_memory_mb": 411.7265625, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.02, "data_distribution": "uniform", "use_indexes": false, "input_size": 500, "total_rows": 500, "rows_scanned": 500, "rows_filtered": 11, "qualifying_rows": 11, "scan_ratio": 1.0, "filter_efficiency": 0.022, "actual_selectivity": 0.022, "expected_selectivity": 0.02, "selectivity_accuracy": 0.998, "comparisons": 1203, "arithmetic_operations": 22, "memory_accesses": 511, "index_lookups": 0, "comparisons_per_row": 2.41, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.978, "index_effectiveness": 0, "scan_time_ms": 0.292, "revenue": 30429.48, "avg_revenue_per_row": 2766.32, "price_variance": 832793760.8, "discount_variance": 0.0, "quantity_variance": 30.25, "effective_scan_size": 500, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=500]", "theoretical_space_complexity": "O(k) [k≈10]", "theoretical_memory_mb": 0.0006103515625, "efficiency_ratio": 0.0}]